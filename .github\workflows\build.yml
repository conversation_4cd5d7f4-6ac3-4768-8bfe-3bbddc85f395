name: Build

on:
  push:
    tags:
      - 'v*'
  
  workflow_dispatch:
  
  pull_request:

jobs:
  build:
    strategy:
      matrix:
        os: [windows-latest, ubuntu-latest, macos-latest]
        debug: [debug, release]
        include:
          - os: windows-latest
            build: BiliTicketRush-${{ github.event_name == 'pull_request' && github.head_ref || github.ref_name }}-Windows
          - os: ubuntu-latest
            build: BiliTicketRush-${{ github.event_name == 'pull_request' && github.head_ref || github.ref_name }}-Linux
          - os: macos-latest
            build: BiliTicketRush-${{ github.event_name == 'pull_request' && github.head_ref || github.ref_name }}-macOS
    runs-on: ${{ matrix.os }}
    env:
      debug: ${{ matrix.debug }}
      build_type: ${{ matrix.debug == 'release' && '正式版' || 'debug' }}
      CARGO_TERM_COLOR: always
    steps:
    - uses: actions/checkout@v4
    - name: Print github.ref
      run: echo "${{ github.ref }}"
    - name: Build Release
      if: matrix.debug == 'release'
      run: cargo build --verbose --release
    - name: Build Debug
      if: matrix.debug == 'debug'
      run: cargo build --verbose
    - name: Copy artifacts on windows
      if: matrix.os == 'windows-latest'
      run: |
        mkdir -p target/output
        cp target/${{matrix.debug}}/frontend.exe target/output/${{ env.build_type }}-${{ matrix.build }}.exe
        cp target/${{matrix.debug}}/frontend.pdb target/output/${{ env.build_type }}-${{ matrix.build }}.pdb
    - name: Copy artifacts on other systems
      if: matrix.os != 'windows-latest'
      run: |
        mkdir -p target/output
        cp target/${{matrix.debug}}/frontend target/output/${{ env.build_type }}-${{ matrix.build }}
    - name: Upload Artifacts
      uses: actions/upload-artifact@v4
      with:
          name: ${{ env.build_type }}-${{ matrix.build }}
          path: target/output
  publish:
    needs: build
    if: startsWith(github.ref, 'refs/tags/')
    runs-on: ubuntu-latest
    steps:
      - name: Print github.ref
        run: echo "${{ github.ref }}"
      - run: echo "publish running"

      - name: Download all workflow artifacts
        uses: actions/download-artifact@v4
        with:
          path: artifacts
      
      - name: List artifacts
        run: ls -la artifacts

      - name: Prepare release assets
        run: |
          mkdir release-assets
          find artifacts -type f -not -name "*.pdb" -exec cp {} release-assets/ \;
          
      - name: Create Release
        uses: softprops/action-gh-release@v2
        with:
          files: release-assets/*
          name: Release ${{ github.ref_name }}
          draft: false
          prerelease: false
          generate_release_notes: true
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}