[package]
name = "common"
version = "0.1.0"
edition = "2024"

[dependencies]
tokio = { version = "1", features = ["full"] }
rand = "0.8"

eframe = { version = "0.23.0", features = ["default"] }
egui = "0.31.0"

# log
log = "0.4.27"
env_logger = "0.9"
chrono = "0.4"
once_cell = "1.8"

#json
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

reqwest = { version="0.11.22", features=["json", "blocking", "cookies"]}

base64 = "0.21"
aes = "0.7.5"
block-modes = "0.8.1"
machine-uid = "0.5.3"
lazy_static = "1.4"
image = "0.25"

whoami = "1.5.1"   
ctrlc = "3.4.0"   

#captcha
bili_ticket_gt = { git = "https://github.com/Amorter/biliTicker_gt",branch = "rust"}

md5 = "0.7.0"

uuid = { version = "1.0", features = ["v4", "fast-rng"] }

hmac = "0.12.1"
sha2 = "0.10.7"
hex = "0.4.3"
cookie = "0.16"
fs2 = "0.4.3"  # 添加对fs2的依赖

#Singleton
single-instance = "0.3.3"