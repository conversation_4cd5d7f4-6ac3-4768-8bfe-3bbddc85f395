[package]
name = "frontend"
version = "0.1.0"
edition = "2024"

[dependencies]
eframe = { version = "0.23.0", features = ["default"] }
egui = "0.31.0"
winapi = { version = "0.3.9", features = ["winuser", "windef"] }

# 字体依赖
egui_extras = { version = "0.23.0", features = ["all_loaders"] }

# 背景图片
image = "0.25"

#时间
chrono = "0.4"

#日志
log = "0.4"
env_logger = "0.9"

#序列化
base64 = "0.13"

#str转二维码
qrcode = "0.14.1"


#网络请求
reqwest = { version="0.11.22", features=["json", "blocking", "cookies"]}
tokio = { version = "1", features = ["full"] }


#rand
rand = "0.8"

#json
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

#jsonwebtoken
jsonwebtoken = "9"



common = {path = "../common"}
backend = {path = "../backend"}

