[package]
name = "backend"
version = "0.1.0"
edition = "2024"

[dependencies]
common = { path = "../common" }
tokio = { version = "1", features = ["full"] }
uuid = { version = "1.3", features = ["v4"] }
chrono = "0.4"

# log
log = "0.4"
env_logger = "0.9"


#json
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

#reqwest
reqwest = { version="0.11.22", features=["json", "blocking"]}

#rand
rand = "0.8"

base64 = "0.22"