# BiliTicketRush
**关键词**: CP31, 哔哩哔哩, 会员购, BW, 自动抢票, 脚本, 抢票

> 一款简单易用的哔哩哔哩会员购自动抢票工具，基于Rust开发的跨平台GUI应用。

BiliTicketRush 是专为B站会员购票务设计的抢票辅助软件，支持多种票务类型（CP、演唱会等）。通过定时抢票和捡漏功能，提高您获取心仪票务的机会。

**重要声明：叔叔于近期对bw项目更新了检测手段（ctoken,ptoken），目前本软件对此没有头绪，因此！使用本软件会被！检测！b站肯定不是莫名其妙加的检测，检测后会怎么样完全！不！知！道！！使用本软件造成的一切后果自负！**‘

**重要声明：本项目仅供学习、研究与技术交流使用，严禁用于任何商业用途或违反中华人民共和国及相关地区法律法规的行为。请用户自觉遵守相关法律法规，合理使用本软件。请于下载后24小时内删除**

**如本项目内容存在任何侵权、违规或不适宜公开之处，请相关权利人或监管部门通过 issue（议题） 或邮箱等与我们联系，我们将在收到通知后第一时间下架或删除相关内容。**

请注意，使用本项目即代表您同意以下条款：

- 不得在国内社群明示本项目（包括biliticket其他项目）包括但不限于截图，链接等。
- 我们的服务是尽最大限度提供的，若您因使用本程序造成的一切经济、法律后果由您个人承担
- 不得使用本项目以任何形式牟利

若您违反上述条款，biliticket 有权终止对您及关联实体、账号继续提供服务。

**本项目作者团队未以任何形式通过本项目获利！本软件完全开源免费！！如果您是从黄牛处购买的本软件，那么您上当了！**

biliticket有权随时修改本条款，请及时关注本页更新


## 导航

[功能特点](#功能特点) • [截图展示](#截图展示) • [安装指南](#安装指南) • [使用教程](#使用教程) • [常见问题](#常见问题) • [参与贡献](#参与贡献) • [许可证](#许可证)

> **注意**：本项目仅供学习研究使用，请合理使用，尊重官方规则。

## 欢迎各位大佬为本项目建言献策以及提出PR

## 功能特点

✅ **用户友好的图形界面**：操作简单直观，易于上手  
✅ **多账号管理**：支持多B站账号同时登录和管理  
✅ **多种抢票模式**：  
   - 定时抢票：在票务开售时精确抢票  
   - 捡漏模式：持续监控回流票并自动抢购  
✅ **购票人管理**：支持添加和管理多个购票人信息  
✅ **实名/非实名票**：同时支持实名制和非实名制票务  
✅ **风控处理**：内置验证码识别和风控处理机制  
✅ **订单管理**：查看和管理抢票成功的订单  
✅ **高性能**：基于Rust语言开发，性能优异  
✅ **通知提醒**：抢票成功后多渠道推送通知

## 截图展示

<!-- 这里可以添加应用截图 -->

## 安装指南

### 方法一：直接下载预编译版本

1. 前往 [GitHub Releases](https://github.com/biliticket/bili_ticket_rush/releases) 页面
2. 下载适合您操作系统的最新版本
3. 确保电脑能连接到互联网
4. 运行应用程序

### 方法二：从源码编译

确保您已安装 Rust 环境，然后执行：

```bash
# 克隆仓库
git clone https://github.com/biliticket/bili_ticket_rush.git
cd bili_ticket_rush

# 编译并运行
cargo build --release
./target/release/frontend.exe
```
或直接使用
```bash
cargo run
```
## 使用教程

### 1. 登录账号
- 启动应用程序后，点击左侧的"账号"标签
- 选择"添加账号"并使用B站账号登录
- 支持扫码登录或短信验证码登录两种方式

### 2. 添加购票人信息
- 在成功登录后，点击"购票人管理"
- 选择"添加购票人"填写实名信息
- 您可以添加多个购票人，方便后续选择

### 3. 选择演出和票种
- 在"主页"标签中输入对应的展览id
- 选择自动模式，点击开始抢票进入详情页
- 选择场次和票种（未开售项目点击最右边的“未开售按钮”即可）
- 选择购票人后，点击页面右下角的“确定”按钮，即可看到倒计时（ps:选择购票人窗口可拖动，如显示不全可按住窗口往上拖即可看到按钮）

### 4. 设置抢票

#### 定时抢票模式
- 选择"定时抢票"模式
- 系统会自动获取官方开票时间
- 选择购票人
- 点击"开始抢票"

#### 捡漏模式
- 选择"捡漏模式"
- 选择购票人
- 点击"开始捡漏"
- 系统会持续监控所有场次和票种，有票即抢

### 5. 查看订单和支付
- 抢票成功后会收到通知提醒，并展示付款二维码
- 在"订单"标签中查看抢票成功的订单
- 请注意支付时限，超时订单会自动取消

## 常见问题

**Q: 支持哪些类型的B站票务？**  
A: 支持B站所有类型的票务，包括演唱会、展览、话剧等实名和非实名票务。

**Q: 同一账号可以同时抢多个场次的票吗？**  
A: 可以，系统支持为同一账号创建多个抢票任务，分别针对不同场次或票种。

**Q: 抢票过程中遇到验证码怎么办？**  
A: 软件内置了验证码处理机制，大部分情况下能自动处理。如果需要手动处理，系统会弹出提示。

**Q: 在部分Windows 及Windows Server系统上使用提示缺少MSVCP140.dll怎么办？**  
A: 这是因为缺少Visual C++ 可再发行软件包。请根据系统类型下载安装：
   - 32位系统：下载并安装 [VC_redist.x86.exe](https://aka.ms/vs/17/release/vc_redist.x86.exe)
   - 64位系统：下载并安装 [VC_redist.x64.exe](https://aka.ms/vs/17/release/vc_redist.x64.exe)
   - 安装完成后请重启系统

**Q: 安装了VC++运行库后程序仍然闪退或电脑没有显卡怎么办？**  
A: 这种情况需要安装Mesa3D软件渲染器，步骤如下：
   1. 参考[这篇文章](https://zhuanlan.zhihu.com/p/666093183)
   2. 访问[Mesa3D发布页](https://github.com/pal1000/mesa-dist-win/releases)下载最新的`mesa3d-x.y.z-release-msvc.7z`文件并解压
   3. 运行解压后的`perappdeploy.cmd`，选择一个空文件夹作为安装路径，全程按'y'确认安装，请注意安装成功后会回到主界面，不要再输入路径重复安装，如果已经再次安装了，那就等他安装完吧（
   4. 运行`systemwidedeploy.cmd`，输入数字'1'，选择第一项，然后按回车键
   5. 完成上述步骤后，重新打开应用程序即可正常运行

**Q: 为什么我看不到日志信息？**  
A: 请确认是否开启了日志记录功能。可在设置中调整日志级别，开发模式下默认显示更详细的日志。


## 免责声明

1. **本软件仅供学习、研究与技术交流使用**，不得用于商业用途。  
2. 使用本软件造成的任何问题由用户自行承担，与开发者无关。  
3. 您应当遵守并了解所在国家或地区的法律法规，若在使用本软件时出现与相关法律法规冲突的行为，请立即停止使用。  
4. 任何用户若利用本软件进行攻击、破坏计算机信息系统、非法获取他人信息或任何其他违法活动，本软件开发者概不负责，亦不提供任何帮助。  
5. **如有任何内容涉嫌侵权、违反法律法规或不适宜公开，请及时通过 issue 或邮箱联系我们，我们将在收到通知后第一时间处理或下架相关内容。**


## 开发计划

- 开发模式和release模式 自动判断日志显示等级
- 未开售项目不支持直接抢票

## 参与贡献

<!-- 可以添加贡献指南 -->

## 许可证

<!-- 可以添加许可证信息 -->
## Star History

<a href="https://star-history.com/#biliticket/bili_ticket_rush&Date">
 <picture>
   <source media="(prefers-color-scheme: dark)" srcset="https://api.star-history.com/svg?repos=biliticket/bili_ticket_rush&type=Date&theme=dark" />
   <source media="(prefers-color-scheme: light)" srcset="https://api.star-history.com/svg?repos=biliticket/bili_ticket_rush&type=Date" />
   <img alt="Star History Chart" src="https://api.star-history.com/svg?repos=biliticket/bili_ticket_rush&type=Date" />
 </picture>
</a>
